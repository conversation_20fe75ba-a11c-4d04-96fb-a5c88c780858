from crewai_tools import BaseTool
from typing import Type, Any, Dict, List
from pydantic import BaseModel, Field
import requests
from bs4 import Beautiful<PERSON>oup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time
import re
import json
import config

class UniversityScrapingInput(BaseModel):
    """Input schema for UniversityScrapingTool."""
    url: str = Field(..., description="The URL of the university website to scrape for program information")

class UniversityScrapingTool(BaseTool):
    name: str = "University Program Scraper"
    description: str = (
        "A comprehensive tool for scraping university websites to extract information about "
        "all academic programs offered. This tool can handle both static and dynamic content, "
        "navigate through multiple pages, and extract detailed program information including "
        "program names, types, descriptions, departments, and requirements."
    )
    args_schema: Type[BaseModel] = UniversityScrapingInput

    def _run(self, url: str) -> str:
        """
        Scrape university website for program information.
        
        Args:
            url: The URL of the university website
            
        Returns:
            JSON string containing extracted program information
        """
        try:
            # First try with requests and BeautifulSoup for static content
            programs_data = self._scrape_with_requests(url)
            
            # If no programs found, try with Selenium for dynamic content
            if not programs_data or len(programs_data) == 0:
                programs_data = self._scrape_with_selenium(url)
            
            # Structure the data
            result = {
                "university_url": url,
                "total_programs": len(programs_data),
                "programs": programs_data,
                "extraction_method": "automated_scraping",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            return json.dumps(result, indent=2, ensure_ascii=False)
            
        except Exception as e:
            return json.dumps({
                "error": f"Failed to scrape university website: {str(e)}",
                "university_url": url,
                "programs": [],
                "total_programs": 0
            }, indent=2)

    def _scrape_with_requests(self, url: str) -> List[Dict[str, Any]]:
        """Scrape using requests and BeautifulSoup for static content."""
        programs = []
        
        try:
            headers = {'User-Agent': config.USER_AGENT}
            response = requests.get(url, headers=headers, timeout=config.REQUEST_TIMEOUT)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for common program-related keywords and sections
            program_keywords = [
                'programs', 'degrees', 'majors', 'courses', 'academics', 
                'undergraduate', 'graduate', 'bachelor', 'master', 'phd',
                'departments', 'schools', 'colleges'
            ]
            
            # Find potential program links and sections
            program_links = self._find_program_links(soup, program_keywords)
            
            # Extract programs from current page
            programs.extend(self._extract_programs_from_soup(soup))
            
            # Visit program-related pages
            for link in program_links[:10]:  # Limit to avoid too many requests
                try:
                    if link.startswith('/'):
                        link = url.rstrip('/') + link
                    elif not link.startswith('http'):
                        continue
                        
                    time.sleep(1)  # Be respectful
                    page_response = requests.get(link, headers=headers, timeout=config.REQUEST_TIMEOUT)
                    page_soup = BeautifulSoup(page_response.content, 'html.parser')
                    programs.extend(self._extract_programs_from_soup(page_soup))
                    
                except Exception as e:
                    continue
                    
        except Exception as e:
            print(f"Error in requests scraping: {e}")
            
        return programs
